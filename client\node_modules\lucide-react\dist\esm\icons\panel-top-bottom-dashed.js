/**
 * @license lucide-react v0.543.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M14 15h1", key: "171nev" }],
  ["path", { d: "M14 9h1", key: "l0svgy" }],
  ["path", { d: "M19 15h2", key: "1vnucp" }],
  ["path", { d: "M19 9h2", key: "te2zfg" }],
  ["path", { d: "M3 15h2", key: "8bym0q" }],
  ["path", { d: "M3 9h2", key: "1h4ldw" }],
  ["path", { d: "M9 15h1", key: "1tg3ks" }],
  ["path", { d: "M9 9h1", key: "15jzuz" }],
  ["rect", { x: "3", y: "3", width: "18", height: "18", rx: "2", key: "h1oib" }]
];
const PanelTopBottomDashed = createLucideIcon("panel-top-bottom-dashed", __iconNode);

export { __iconNode, PanelTopBottomDashed as default };
//# sourceMappingURL=panel-top-bottom-dashed.js.map
