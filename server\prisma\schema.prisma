// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  // output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Role {
  ADMIN
  USER
}

model User {
  //id        Int       @id @default(autoincrement())
  id        String    @id @default(uuid())
  email     String    @unique
  password  String
  name      String?
  role      Role      @default(USER)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  bio       String?
  posts     Post[]
  comments  Comment[]
}

model Post {
  //id        Int       @id @default(autoincrement())
  id          String     @id @default(uuid())
  title       String
  content     String?
  published   Boolean    @default(false)
  author      User       @relation(fields: [authorId], references: [id])
  authorId    String
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  publishedAt DateTime?
  imageURL    String?
  views       Int        @default(0)
  comments    Comment[]
  category    Category[]
}

model Comment {
  //id        Int       @id @default(autoincrement())
  id        String   @id @default(uuid())
  content   String
  createdAt DateTime @default(now())
  editedAt  DateTime @default(now())
  author    User     @relation(fields: [authorId], references: [id])
  authorId  String
  post      Post     @relation(fields: [postId], references: [id])
  postId    String
}

model Category {
  //id        Int       @id @default(autoincrement())
  id    String @id @default(uuid())
  name  String
  posts Post[]
}
