/**
 * @license lucide-react v0.543.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 22a2 2 0 0 1-2-2", key: "i7yj1i" }],
  ["path", { d: "M16 22h-2", key: "18d249" }],
  [
    "path",
    {
      d: "M16 4a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h3a1 1 0 0 0 1-1v-5a2 2 0 0 1 2-2h5a1 1 0 0 0 1-1z",
      key: "1njgbb"
    }
  ],
  ["path", { d: "M20 8a2 2 0 0 1 2 2", key: "1770vt" }],
  ["path", { d: "M22 14v2", key: "iot8ja" }],
  ["path", { d: "M22 20a2 2 0 0 1-2 2", key: "qj8q6g" }]
];
const SquaresSubtract = createLucideIcon("squares-subtract", __iconNode);

export { __iconNode, SquaresSubtract as default };
//# sourceMappingURL=squares-subtract.js.map
