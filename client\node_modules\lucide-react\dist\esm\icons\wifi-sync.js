/**
 * @license lucide-react v0.543.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M11.965 10.105v4L13.5 12.5a5 5 0 0 1 8 1.5", key: "1immaq" }],
  ["path", { d: "M11.965 14.105h4", key: "uejny8" }],
  ["path", { d: "M17.965 18.105h4L20.43 19.71a5 5 0 0 1-8-1.5", key: "1i3a7e" }],
  ["path", { d: "M2 8.82a15 15 0 0 1 20 0", key: "dnpr2z" }],
  ["path", { d: "M21.965 22.105v-4", key: "1ku6vx" }],
  ["path", { d: "M5 12.86a10 10 0 0 1 3-2.032", key: "pemdtu" }],
  ["path", { d: "M8.5 16.429h.01", key: "2bm739" }]
];
const WifiSync = createLucideIcon("wifi-sync", __iconNode);

export { __iconNode, WifiSync as default };
//# sourceMappingURL=wifi-sync.js.map
