// Gemini script

{
  "compilerOptions": {
    "target": "ES2020",
    "module": "NodeNext",
    "moduleResolution": "NodeNext",
    "lib": ["ES2020"],
    "outDir": "build",
    "rootDir": "src",
    "allowImportingTsExtensions": true,
    "noEmit": true,

    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "sourceMap": true,
    "declaration": true,
    "declarationMap": true,

    "resolveJsonModule": true,

    // The "verbatimModuleSyntax" and "isolatedModules" options can cause issues
    // with certain TypeScript features like enums in a ts-node environment.
    // Removing them allows ts-node to correctly transpile the code.
    // "verbatimModuleSyntax": true,
    // "isolatedModules": true,

    // Optional: if using path aliases
    "baseUrl": "./src",
    "paths": {
      "@api/*": ["api/*"],
      "@routes/*": ["routes/*"],
      "@modules/*": ["modules/*"],
      "@utils/*": ["utils/*"]
    }
  },
  "include": ["src"],
  "exclude": ["node_modules", "build"]
}

// GPT generated

/*{
  "compilerOptions": {
    "target": "ES2020",
    "module": "NodeNext",
    "moduleResolution": "NodeNext",
    "lib": ["ES2020"],
    "outDir": "build",
    "rootDir": "src",
    "allowImportingTsExtensions": true,

    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "sourceMap": true,
    "declaration": true,
    "declarationMap": true,

    "verbatimModuleSyntax": true,
    "resolveJsonModule": true,

    // Optional: if using path aliases
    "baseUrl": "./src",
    "paths": {
      "@api/*": ["api/*"],
      "@routes/*": ["routes/*"],
      "@modules/*": ["modules/*"],
      "@utils/*": ["utils/*"]
    }
  },
  "include": ["src"],
  "exclude": ["node_modules", "build"]
} */

/* Original script

{
  // Visit https://aka.ms/tsconfig to read more about this file
  "compilerOptions": {
    // File Layout
    // "rootDir": "./src",
    "outDir": "build",  // "./dist",

    // Environment Settings
    // See also https://aka.ms/tsconfig/module
    "module": "nodenext",
    "target": "es2017",
    "types": [],
    // For nodejs:
    "lib": ["es2017"],
    // "types": ["node"],
    // and npm install -D @types/node

    // Other Outputs
    "sourceMap": true,
    "declaration": true,
    "declarationMap": true,

    // Stricter Typechecking Options
    "noUncheckedIndexedAccess": true,
    "exactOptionalPropertyTypes": true,

    // Style Options
    // "noImplicitReturns": true,
    // "noImplicitOverride": true,
    // "noUnusedLocals": true,
    // "noUnusedParameters": true,
    // "noFallthroughCasesInSwitch": true,
    // "noPropertyAccessFromIndexSignature": true,
    
    // Recommended Options
    "strict": true,
    "jsx": "react-jsx",
    "verbatimModuleSyntax": true,
    "isolatedModules": true,
    "noUncheckedSideEffectImports": true,
    "moduleDetection": "force",
    "skipLibCheck": true,
  }
}*/
