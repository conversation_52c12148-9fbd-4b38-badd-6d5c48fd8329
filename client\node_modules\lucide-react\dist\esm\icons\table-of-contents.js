/**
 * @license lucide-react v0.543.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M16 5H3", key: "m91uny" }],
  ["path", { d: "M16 12H3", key: "1a2rj7" }],
  ["path", { d: "M16 19H3", key: "zzsher" }],
  ["path", { d: "M21 5h.01", key: "wa75ra" }],
  ["path", { d: "M21 12h.01", key: "msek7k" }],
  ["path", { d: "M21 19h.01", key: "qvbq2j" }]
];
const TableOfContents = createLucideIcon("table-of-contents", __iconNode);

export { __iconNode, TableOfContents as default };
//# sourceMappingURL=table-of-contents.js.map
