import type { Request, Response } from 'express';
import * as CategoryServices from './category.services.ts';

export const getCategories = async (req: Request, res: Response) => {
  try {
    const posts = await CategoryServices.getCategories();
    return res.status(200).json(posts);
  } catch (error: any) {
    return res.status(500).json(error.message);
  }
};

export const getCategory = async (req: Request, res: Response) => {
  const id: number = parseInt(req.params.id, 10);

  try {
    const uniquePost = await CategoryServices.getCategory(id);
    return res.status(200).json(uniquePost);
  } catch (error: any) {
    return res.status(500).json(error.message);
  }
};

export const addCategory = async (req: Request, res: Response) => {
  try {
    const newPost = await CategoryServices.addCategory(req.body);
    return res.status(201).json(newPost);
  } catch (error: any) {
    return res.json(error.message);
  }
};

export const editCategory = async (req: Request, res: Response) => {
  const id: number = parseInt(req.params.id, 10);

  try {
    const updateAuthorPost = await CategoryServices.editCategory(id, req.body);
    return res.status(200).json(updateAuthorPost);
  } catch (error: any) {
    return res.status(500).json(error.message);
  }
};

export const deleteCategory = async (req: Request, res: Response) => {
  const id: number = parseInt(req.params.id, 10);

  try {
    const delPost = await CategoryServices.deleteCategory(id);
    return res.json({ data: delPost, message: 'Category deleted' });
  } catch (error: any) {
    return res.json(error.message);
  }
};
