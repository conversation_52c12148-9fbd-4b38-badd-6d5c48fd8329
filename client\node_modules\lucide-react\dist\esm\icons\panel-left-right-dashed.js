/**
 * @license lucide-react v0.543.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M15 10V9", key: "4dkmfx" }],
  ["path", { d: "M15 15v-1", key: "6a4afx" }],
  ["path", { d: "M15 21v-2", key: "1qshmc" }],
  ["path", { d: "M15 5V3", key: "1fk0mb" }],
  ["path", { d: "M9 10V9", key: "1lazqi" }],
  ["path", { d: "M9 15v-1", key: "9lx740" }],
  ["path", { d: "M9 21v-2", key: "1fwk0n" }],
  ["path", { d: "M9 5V3", key: "2q8zi6" }],
  ["rect", { x: "3", y: "3", width: "18", height: "18", rx: "2", key: "h1oib" }]
];
const PanelLeftRightDashed = createLucideIcon("panel-left-right-dashed", __iconNode);

export { __iconNode, PanelLeftRightDashed as default };
//# sourceMappingURL=panel-left-right-dashed.js.map
